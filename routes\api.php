<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PostController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// ---------------------------
// Public API Routes (غير محمية)
// ---------------------------
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// ---------------------------
// API Routes (محمي بـ Sanctum)
// ---------------------------
Route::middleware('auth:sanctum')->group(function () {

    // Auth Routes
    Route::post('/logout', [AuthController::class, 'logout']);       // تسجيل الخروج
    Route::get('/me', [AuthController::class, 'me']);                // معلومات المستخدم الحالي

    // User Routes
    Route::get('/user', [UserController::class, 'me']);              // معلومات المستخدم الحالي
    Route::get('/users', [UserController::class, 'index']);          // جلب جميع المستخدمين
    Route::get('/users/{user}', [UserController::class, 'show']);    // جلب مستخدم واحد
    Route::get('/users/{user}/posts', [UserController::class, 'posts']); // مقالات مستخدم معين

    // Posts Routes
    Route::get('/posts', [PostController::class, 'apiIndex']);       // جلب كل البوستات
    Route::get('/posts/{post}', [PostController::class, 'apiShow']); // جلب بوست واحد
    Route::post('/posts', [PostController::class, 'apiStore']);      // إنشاء بوست جديد
    Route::put('/posts/{post}', [PostController::class, 'apiUpdate']); // تحديث بوست
    Route::delete('/posts/{post}', [PostController::class, 'apiDestroy']); // حذف بوست
});
