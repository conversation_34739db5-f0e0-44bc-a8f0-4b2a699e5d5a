<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Post;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدمين تجريبيين
        $user1 = User::create([
            'name' => 'Ahmed Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $user2 = User::create([
            'name' => '<PERSON> Mohamed',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // إنشاء مقالات تجريبية
        $posts = [
            [
                'title' => 'Python Programming Basics',
                'description' => 'Learn Python programming language from scratch. This comprehensive guide covers variables, functions, loops, and object-oriented programming concepts. Perfect for beginners who want to start their programming journey with Python.',
                'user_id' => $user1->id,
            ],
            [
                'title' => 'Laravel Framework Guide',
                'description' => 'Master Laravel PHP framework with this detailed tutorial. Learn about routing, controllers, models, views, and database operations. Build modern web applications with Lara<PERSON>\'s elegant syntax and powerful features.',
                'user_id' => $user1->id,
            ],
            [
                'title' => 'JavaScript ES6 Features',
                'description' => 'Explore the latest JavaScript ES6 features including arrow functions, destructuring, promises, and async/await. Understand how these features can improve your code quality and development productivity.',
                'user_id' => $user2->id,
            ],
            [
                'title' => 'React.js Development',
                'description' => 'Build interactive user interfaces with React.js. Learn about components, state management, hooks, and routing. Create dynamic web applications with this popular JavaScript library.',
                'user_id' => $user2->id,
            ],
            [
                'title' => 'Database Design Principles',
                'description' => 'Learn the fundamentals of database design including normalization, relationships, indexing, and query optimization. Design efficient and scalable database schemas for your applications.',
                'user_id' => $user1->id,
            ],
        ];

        foreach ($posts as $postData) {
            Post::create($postData);
        }
    }
}
