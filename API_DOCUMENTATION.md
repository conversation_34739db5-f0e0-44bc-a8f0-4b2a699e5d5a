# 📚 API Documentation

## 🔐 Authentication
All API endpoints require authentication using Laravel Sanctum.

### Headers Required:
```
Authorization: Bearer {your-token}
Accept: application/json
Content-Type: application/json
```

---

## 📝 Posts Endpoints

### 1️⃣ Get All Posts
```http
GET /api/posts
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Python Programming",
      "description": "Learn Python programming...",
      "created_at": "2025-08-18T12:57:39.000000Z",
      "updated_at": "2025-08-18T12:57:39.000000Z",
      "created_at_human": "2 hours ago",
      "created_at_formatted": "Sunday, August 18, 2025 at 12:57 PM",
      "author": {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>"
      },
      "excerpt": "Learn Python programming language with AI...",
      "word_count": 23,
      "reading_time": "1 min read"
    }
  ],
  "meta": {
    "total_posts": 1,
    "authors_count": 1,
    "latest_post": "2025-08-18T12:57:39.000000Z"
  },
  "message": "Posts retrieved successfully",
  "status": "success"
}
```

### 2️⃣ Get Single Post
```http
GET /api/posts/{id}
```

### 3️⃣ Create New Post
```http
POST /api/posts
```

**Body:**
```json
{
  "title": "New Post Title",
  "description": "Post content here (minimum 10 characters)"
}
```

**Validation Rules:**
- `title`: required, minimum 3 characters, unique
- `description`: required, minimum 10 characters

### 4️⃣ Update Post
```http
PUT /api/posts/{id}
```

**Body:** Same as create
**Note:** Only the post author can update their posts

### 5️⃣ Delete Post
```http
DELETE /api/posts/{id}
```

**Note:** Only the post author can delete their posts

---

## 👤 User Endpoints

### 1️⃣ Get Current User
```http
GET /api/user
```

### 2️⃣ Get All Users
```http
GET /api/users
```

### 3️⃣ Get Single User
```http
GET /api/users/{id}
```

### 4️⃣ Get User's Posts
```http
GET /api/users/{id}/posts
```

---

## 🔧 Testing with cURL

### Get Posts:
```bash
curl -X GET "http://127.0.0.1:8000/api/posts" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### Create Post:
```bash
curl -X POST "http://127.0.0.1:8000/api/posts" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "title": "My New Post",
    "description": "This is the content of my new post with more than 10 characters."
  }'
```

### Update Post:
```bash
curl -X PUT "http://127.0.0.1:8000/api/posts/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "title": "Updated Post Title",
    "description": "Updated content with more than 10 characters."
  }'
```

### Delete Post:
```bash
curl -X DELETE "http://127.0.0.1:8000/api/posts/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

---

## 📊 Features

### ✅ Implemented:
- ✅ Full CRUD operations for Posts
- ✅ Form Request validation (Store & Update)
- ✅ Laravel Breeze authentication
- ✅ API endpoints with Sanctum authentication
- ✅ API Resources with User relationships
- ✅ Pagination on Index page
- ✅ Carbon date formatting
- ✅ Authorization (users can only edit/delete their own posts)

### 🎯 Validation Rules:
- **Title**: Required, minimum 3 characters, unique
- **Description**: Required, minimum 10 characters
- **Update**: Title uniqueness ignores current post

### 🕒 Date Formatting:
- **Human readable**: "2 hours ago"
- **Formatted**: "Sunday, August 18, 2025 at 12:57 PM"
- **ISO**: Standard Laravel timestamps

### 🔒 Security:
- All API routes protected with `auth:sanctum`
- Users can only modify their own posts
- CSRF protection on web routes
- Form validation on all inputs
